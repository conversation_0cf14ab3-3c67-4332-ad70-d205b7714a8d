# SAM - نظام إدارة شؤون الموظفين

## وصف المشروع

نظام شؤون موظفين احترافي يعمل على سطح المكتب أو عبر الويب، يُمكّن إدارات الموارد البشرية من إدارة جميع تفاصيل الموظفين، متابعة الدوام، الإجازات، العقوبات، الحوافز، الرواتب، العقود، التأمينات، والصلاحيات، مع دعم للتقارير الدقيقة والتصدير والطباعة، بواجهة مستخدم سهلة ومنظمة.

**المطور:** MOHANNAD AHMAD  
**الهاتف:** +************

## الميزات الرئيسية

### 🧑‍💼 إدارة الموظفين
- إضافة/تعديل/حذف بيانات الموظفين
- رقم وظيفي تلقائي
- رفع الصور الشخصية والمستندات
- إدارة البيانات الشخصية والوظيفية
- تتبع حالة الموظف (نشط، مجاز، مفصول، موقوف)

### 🕐 إدارة الدوام
- تسجيل الحضور والانصراف
- حساب التأخير والخروج المبكر
- تقارير الحضور التفصيلية
- ربط الدوام بحساب الراتب

### 🌴 إدارة الإجازات
- أنواع إجازات متعددة (سنوية، مرضية، أمومة، إلخ)
- نظام طلب وموافقة الإجازات
- حساب الأرصدة المتبقية
- تقارير مفصلة للإجازات

### 💵 إدارة الرواتب
- حساب الرواتب الإجمالي المبسط
- إدارة البدلات والخصومات الأساسية
- كشوف رواتب شهرية
- حسابات دقيقة للإضافات والخصومات

### 📊 التقارير والإحصائيات
- تقارير شاملة لجميع الأقسام
- تصدير إلى Excel و PDF
- طباعة احترافية
- إحصائيات تفاعلية

### 👥 إدارة المستخدمين والصلاحيات
- نظام تسجيل دخول آمن
- صلاحيات مفصلة حسب الدور
- سجل العمليات (Audit Log)

## التقنيات المستخدمة

- **Frontend:** HTML5, CSS3, JavaScript (ES6+)
- **UI Framework:** Bootstrap 5
- **Icons:** Font Awesome, Bootstrap Icons
- **Charts:** Chart.js
- **Export:** jsPDF, SheetJS (XLSX)
- **Storage:** localStorage (قاعدة بيانات محلية)
- **Fonts:** Google Fonts (Cairo)

## متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- JavaScript مفعل
- لا يتطلب خادم أو قاعدة بيانات خارجية

## طريقة التشغيل

1. قم بتحميل المشروع
2. افتح ملف `index.html` في المتصفح
3. استخدم بيانات الدخول الافتراضية:
   - **المدير العام:** `admin` / `admin123`
   - **الموارد البشرية:** `hr` / `hr123`
   - **المحاسب:** `accountant` / `acc123`

## هيكل المشروع

```
SAM/
├── index.html              # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   └── style.css       # ملف الأنماط الرئيسي
│   └── js/
│       ├── app.js          # التطبيق الرئيسي
│       ├── auth.js         # إدارة المصادقة
│       ├── database.js     # قاعدة البيانات المحلية
│       ├── employees.js    # إدارة الموظفين
│       ├── attendance.js   # إدارة الدوام
│       ├── leaves.js       # إدارة الإجازات
│       ├── payroll.js      # إدارة الرواتب
│       └── reports.js      # التقارير
└── README.md              # هذا الملف
```

## الوحدات الرئيسية

### 1. وحدة المصادقة (auth.js)
- تسجيل الدخول والخروج
- إدارة المستخدمين والأدوار
- التحقق من الصلاحيات

### 2. قاعدة البيانات المحلية (database.js)
- تخزين البيانات في localStorage
- عمليات CRUD الأساسية
- النسخ الاحتياطي والاستعادة

### 3. إدارة الموظفين (employees.js)
- إضافة وتعديل بيانات الموظفين
- رفع الصور والمستندات
- البحث والفلترة

### 4. إدارة الدوام (attendance.js)
- تسجيل الحضور والانصراف
- حساب ساعات العمل
- تقارير الحضور

### 5. إدارة الإجازات (leaves.js)
- طلب الإجازات
- نظام الموافقات
- حساب الأرصدة

### 6. إدارة الرواتب (payroll.js)
- حساب الرواتب
- إدارة البدلات والخصومات
- كشوف الرواتب

### 7. التقارير (reports.js)
- تقارير شاملة
- تصدير وطباعة
- إحصائيات تفاعلية

## الأدوار والصلاحيات

### مدير النظام (Admin)
- جميع الصلاحيات
- إدارة المستخدمين
- النسخ الاحتياطي

### مدير الموارد البشرية (HR Manager)
- إدارة الموظفين
- إدارة الدوام والإجازات
- إدارة الرواتب
- التقارير

### المحاسب (Accountant)
- إدارة الرواتب
- التقارير المالية

### الموظف (Employee)
- عرض بياناته الشخصية
- طلب الإجازات
- عرض كشف الراتب

## المميزات التقنية

- **تصميم متجاوب:** يعمل على جميع الأجهزة
- **واجهة عربية:** دعم كامل للغة العربية
- **تخزين محلي:** لا يتطلب خادم
- **أمان:** تشفير البيانات الحساسة
- **سرعة:** تحميل سريع وأداء عالي

## التطوير المستقبلي

- [ ] دعم قاعدة بيانات خارجية
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة خارجية
- [ ] تقارير متقدمة
- [ ] نظام إشعارات

## الدعم والتواصل

للدعم التقني أو الاستفسارات:
- **المطور:** MOHANNAD AHMAD
- **الهاتف:** +************
- **البريد الإلكتروني:** [يرجى إضافة البريد الإلكتروني]

## الترخيص

هذا المشروع مطور خصيصاً لإدارة شؤون الموظفين. جميع الحقوق محفوظة للمطور.

---

**SAM - نظام إدارة شؤون الموظفين**  
*حلول متكاملة لإدارة الموارد البشرية*
