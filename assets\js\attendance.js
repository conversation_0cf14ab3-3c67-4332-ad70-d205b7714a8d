/**
 * SAM - نظام إدارة شؤون الموظفين
 * Attendance Management Module
 * وحدة إدارة الدوام
 */

class AttendanceManager {
    constructor() {
        this.currentView = 'daily';
        this.selectedDate = new Date().toISOString().split('T')[0];
        this.selectedWeek = '';
        this.selectedMonth = new Date().toISOString().slice(0, 7);
        this.selectedEmployee = '';
        this.attendanceType = '';
        this.editingAttendanceId = null;
        this.autoRefreshInterval = null;
    }

    render() {
        if (!window.authManager.hasPermission('attendance')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.toggleDateFilters();
        this.loadTodayAttendance();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-clock me-2"></i>
                            إدارة الدوام
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" id="checkInBtn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل حضور
                            </button>
                            <button class="btn btn-success" id="checkOutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل انصراف
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="presentCount">0</h4>
                                    <p class="mb-0">حاضر اليوم</p>
                                </div>
                                <i class="fas fa-user-check fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="lateCount">0</h4>
                                    <p class="mb-0">متأخر</p>
                                </div>
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="absentCount">0</h4>
                                    <p class="mb-0">غائب</p>
                                </div>
                                <i class="fas fa-user-times fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="leaveCount">0</h4>
                                    <p class="mb-0">في إجازة</p>
                                </div>
                                <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter and View Controls -->
            <div class="row mb-4">
                <div class="col-md-3" id="dateFilterContainer">
                    <label class="form-label">التاريخ</label>
                    <input type="date" class="form-control" id="dateFilter" value="${this.selectedDate}">
                </div>
                <div class="col-md-3" id="weekFilterContainer" style="display: none;">
                    <label class="form-label">الأسبوع</label>
                    <input type="week" class="form-control" id="weekFilter">
                </div>
                <div class="col-md-3" id="monthFilterContainer" style="display: none;">
                    <label class="form-label">الشهر</label>
                    <input type="month" class="form-control" id="monthFilter" value="${new Date().toISOString().slice(0, 7)}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الموظف</label>
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع العرض</label>
                    <select class="form-select" id="viewType">
                        <option value="daily">يومي</option>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly">شهري</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary flex-fill" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-outline-success" id="exportAttendanceBtn">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <button class="btn btn-outline-info" id="printAttendanceBtn">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Attendance Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        سجل الحضور والانصراف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="attendanceTable">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                    <th>وقت الحضور</th>
                                    <th>وقت الانصراف</th>
                                    <th>ساعات العمل</th>
                                    <th>الإضافي</th>
                                    <th>الخصومات</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="attendanceTableBody">
                                <!-- Attendance records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Check In/Out Modal -->
            <div class="modal fade" id="attendanceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="attendanceModalTitle">تسجيل حضور</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="attendanceForm">
                                <div class="mb-3">
                                    <label class="form-label">الموظف *</label>
                                    <select class="form-select" name="employee_id" required>
                                        <option value="">اختر الموظف</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" name="date" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوقت *</label>
                                    <input type="time" class="form-control" name="time" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveAttendanceBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Attendance Modal -->
            <div class="modal fade" id="editAttendanceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تعديل سجل الحضور</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editAttendanceForm">
                                <div class="mb-3">
                                    <label class="form-label">وقت الحضور</label>
                                    <input type="time" class="form-control" name="check_in">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">وقت الانصراف</label>
                                    <input type="time" class="form-control" name="check_out">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" name="status">
                                        <option value="present">حاضر</option>
                                        <option value="late">متأخر</option>
                                        <option value="early">مبكر</option>
                                        <option value="absent">غائب</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="updateAttendanceBtn">تحديث</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Check in/out buttons
        document.getElementById('checkInBtn').addEventListener('click', () => {
            this.showAttendanceModal('check_in');
        });

        document.getElementById('checkOutBtn').addEventListener('click', () => {
            this.showAttendanceModal('check_out');
        });

        // Filter events
        document.getElementById('dateFilter').addEventListener('change', (e) => {
            this.selectedDate = e.target.value;
            this.loadAttendanceData();
        });

        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadAttendanceData();
        });

        document.getElementById('viewType').addEventListener('change', (e) => {
            this.currentView = e.target.value;
            this.toggleDateFilters();
            this.loadAttendanceData();
        });

        // Week filter
        document.getElementById('weekFilter').addEventListener('change', (e) => {
            this.selectedWeek = e.target.value;
            this.loadAttendanceData();
        });

        // Month filter
        document.getElementById('monthFilter').addEventListener('change', (e) => {
            this.selectedMonth = e.target.value;
            this.loadAttendanceData();
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            const btn = document.getElementById('refreshBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            btn.disabled = true;

            setTimeout(() => {
                this.loadAttendanceData();
                this.updateStats();
                btn.innerHTML = originalText;
                btn.disabled = false;
                window.samApp.showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 500);
        });

        // Export button
        document.getElementById('exportAttendanceBtn').addEventListener('click', () => {
            this.exportAttendance();
        });

        // Print button
        document.getElementById('printAttendanceBtn').addEventListener('click', () => {
            this.printAttendance();
        });

        // Save attendance
        document.getElementById('saveAttendanceBtn').addEventListener('click', () => {
            this.saveAttendance();
        });

        // Update attendance
        document.getElementById('updateAttendanceBtn').addEventListener('click', () => {
            this.updateAttendance();
        });

        // Auto-refresh every 30 seconds
        this.autoRefreshInterval = setInterval(() => {
            // تحديث صامت للبيانات دون إظهار مؤشر التحميل
            const modal = document.getElementById('attendanceModal');
            const editModal = document.getElementById('editAttendanceModal');

            // تحديث فقط إذا لم يكن هناك نموذج مفتوح
            if ((!modal || !modal.classList.contains('show')) &&
                (!editModal || !editModal.classList.contains('show'))) {
                this.updateStats();
                this.loadAttendanceData();
            }
        }, 30000);
    }

    toggleDateFilters() {
        const dateContainer = document.getElementById('dateFilterContainer');
        const weekContainer = document.getElementById('weekFilterContainer');
        const monthContainer = document.getElementById('monthFilterContainer');

        // Hide all containers first
        dateContainer.style.display = 'none';
        weekContainer.style.display = 'none';
        monthContainer.style.display = 'none';

        // Show the appropriate container based on view type
        switch (this.currentView) {
            case 'daily':
                dateContainer.style.display = 'block';
                break;
            case 'weekly':
                weekContainer.style.display = 'block';
                // Set current week if not set
                if (!this.selectedWeek) {
                    const today = new Date();
                    const year = today.getFullYear();
                    const week = this.getWeekNumber(today);
                    this.selectedWeek = `${year}-W${week.toString().padStart(2, '0')}`;
                    document.getElementById('weekFilter').value = this.selectedWeek;
                }
                break;
            case 'monthly':
                monthContainer.style.display = 'block';
                // Set current month if not set
                if (!this.selectedMonth) {
                    this.selectedMonth = new Date().toISOString().slice(0, 7);
                    document.getElementById('monthFilter').value = this.selectedMonth;
                }
                break;
        }
    }

    getWeekNumber(date) {
        const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
        return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
    }

    loadTodayAttendance() {
        this.loadEmployeeOptions();
        this.loadAttendanceData();
        this.updateStats();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');
        
        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';
            
            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadAttendanceData() {
        let attendance = [];

        switch (this.currentView) {
            case 'daily':
                attendance = Database.getAttendance({ date: this.selectedDate });
                break;
            case 'weekly':
                attendance = this.getWeekAttendance();
                break;
            case 'monthly':
                attendance = this.getMonthAttendance();
                break;
        }

        if (this.selectedEmployee) {
            attendance = attendance.filter(a => a.employee_id === this.selectedEmployee);
        }

        this.renderAttendanceTable(attendance);
        this.updateStats();
    }

    getWeekAttendance() {
        if (!this.selectedWeek) return [];

        // Parse week format YYYY-WXX
        const [year, weekStr] = this.selectedWeek.split('-W');
        const weekNum = parseInt(weekStr);

        // Calculate start and end dates of the week
        const startDate = this.getDateOfWeek(parseInt(year), weekNum);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);

        return Database.getAll('attendance').filter(a => {
            const recordDate = new Date(a.date);
            return recordDate >= startDate && recordDate <= endDate;
        });
    }

    getDateOfWeek(year, week) {
        const firstDayOfYear = new Date(year, 0, 1);
        const daysToAdd = (week - 1) * 7 - firstDayOfYear.getDay();
        return new Date(year, 0, 1 + daysToAdd);
    }

    getMonthAttendance() {
        const month = this.selectedMonth || new Date().toISOString().slice(0, 7);
        return Database.getAttendance({ month });
    }

    renderAttendanceTable(attendance) {
        const tbody = document.getElementById('attendanceTableBody');
        const employees = Database.getEmployees();
        
        if (attendance.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد سجلات حضور</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = attendance.map(record => {
            const employee = employees.find(emp => emp.id === record.employee_id);
            const workingHours = this.calculateWorkingHours(record.check_in, record.check_out);
            const statusBadge = this.getStatusBadge(record.status);

            // Calculate overtime and deductions
            const overtimeData = this.calculateOvertimeAndDeductions(employee, record);

            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee?.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${employee?.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                                <small class="text-muted">${employee?.employee_number || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td>${window.samApp.formatDate(record.date)}</td>
                    <td>
                        <span class="badge bg-success">${record.check_in || '-'}</span>
                    </td>
                    <td>
                        <span class="badge bg-danger">${record.check_out || '-'}</span>
                    </td>
                    <td>${workingHours}</td>
                    <td>
                        ${overtimeData.overtime.hours > 0 ? `
                            <div class="text-success">
                                <small><i class="fas fa-plus-circle"></i> ${overtimeData.overtime.hours}س</small><br>
                                <small>${window.samApp.formatCurrency(overtimeData.overtime.amount)}</small>
                            </div>
                        ` : '<span class="text-muted">-</span>'}
                    </td>
                    <td>
                        ${overtimeData.deductions.hours > 0 ? `
                            <div class="text-danger">
                                <small><i class="fas fa-minus-circle"></i> ${overtimeData.deductions.hours}س</small><br>
                                <small>${window.samApp.formatCurrency(overtimeData.deductions.amount)}</small>
                            </div>
                        ` : '<span class="text-muted">-</span>'}
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary edit-attendance"
                                    data-attendance-id="${record.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-attendance"
                                    data-attendance-id="${record.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // Edit attendance
        document.querySelectorAll('.edit-attendance').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const attendanceId = e.target.closest('.edit-attendance').dataset.attendanceId;
                this.editAttendance(attendanceId);
            });
        });

        // Delete attendance
        document.querySelectorAll('.delete-attendance').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const attendanceId = e.target.closest('.delete-attendance').dataset.attendanceId;
                this.deleteAttendance(attendanceId);
            });
        });
    }

    calculateWorkingHours(checkIn, checkOut) {
        if (!checkIn || !checkOut) return '-';

        const start = new Date(`2000-01-01T${checkIn}`);
        const end = new Date(`2000-01-01T${checkOut}`);
        const diffMs = end - start;
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

        return `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
    }

    calculateOvertimeAndDeductions(employee, record) {
        if (!employee || !record.check_in || !record.check_out) {
            return {
                overtime: { hours: 0, amount: 0 },
                deductions: { hours: 0, amount: 0 }
            };
        }

        const settings = Database.getSettings();
        const workStartTime = employee.work_start_time || settings.working_hours?.start_time || '08:00';
        const workEndTime = employee.work_end_time || settings.working_hours?.end_time || '17:00';
        const breakDuration = (employee.break_duration || settings.working_hours?.break_duration || 60) / 60; // Convert to hours
        const lateThreshold = (employee.late_tolerance || 15) / 60; // Convert to hours
        const overtimeRate = settings.attendance?.overtime_rate || 1.5;
        const hourlyRate = employee.salary ? (employee.salary / 30 / 8) : 0; // Assuming 30 days, 8 hours per day

        // Calculate expected work hours
        const expectedStart = new Date(`2000-01-01T${workStartTime}`);
        const expectedEnd = new Date(`2000-01-01T${workEndTime}`);
        const expectedWorkHours = (expectedEnd - expectedStart) / (1000 * 60 * 60) - breakDuration;

        // Calculate actual work hours
        const actualStart = new Date(`2000-01-01T${record.check_in}`);
        const actualEnd = new Date(`2000-01-01T${record.check_out}`);
        const actualWorkHours = (actualEnd - actualStart) / (1000 * 60 * 60) - breakDuration;

        let overtimeHours = 0;
        let deductionHours = 0;

        // Calculate overtime (work beyond expected hours)
        if (actualWorkHours > expectedWorkHours) {
            overtimeHours = actualWorkHours - expectedWorkHours;
        }

        // Calculate deductions (late arrival or early departure)
        const lateMinutes = Math.max(0, (actualStart - expectedStart) / (1000 * 60) - (lateThreshold * 60));
        const earlyLeaveMinutes = Math.max(0, (expectedEnd - actualEnd) / (1000 * 60));

        if (lateMinutes > 0 || earlyLeaveMinutes > 0) {
            deductionHours = (lateMinutes + earlyLeaveMinutes) / 60;
        }

        // Calculate shortage if worked less than expected
        if (actualWorkHours < expectedWorkHours && record.status !== 'absent') {
            const shortageHours = expectedWorkHours - actualWorkHours;
            deductionHours += shortageHours;
        }

        return {
            overtime: {
                hours: Math.round(overtimeHours * 100) / 100,
                amount: overtimeHours * hourlyRate * overtimeRate
            },
            deductions: {
                hours: Math.round(deductionHours * 100) / 100,
                amount: deductionHours * hourlyRate
            }
        };
    }

    getStatusBadge(status) {
        const statusMap = {
            'present': { class: 'success', text: 'حاضر' },
            'late': { class: 'warning', text: 'متأخر' },
            'early': { class: 'info', text: 'مبكر' },
            'absent': { class: 'danger', text: 'غائب' }
        };
        
        const statusInfo = statusMap[status] || { class: 'secondary', text: 'غير محدد' };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
    }

    updateStats() {
        const todayAttendance = Database.getTodayAttendance();
        const activeLeaves = Database.getActiveLeaves();
        const totalEmployees = Database.getEmployees().filter(emp => emp.status === 'active').length;
        
        const presentCount = todayAttendance.filter(a => a.status === 'present').length;
        const lateCount = todayAttendance.filter(a => a.status === 'late').length;
        const absentCount = totalEmployees - todayAttendance.length - activeLeaves.length;
        const leaveCount = activeLeaves.length;
        
        document.getElementById('presentCount').textContent = presentCount;
        document.getElementById('lateCount').textContent = lateCount;
        document.getElementById('absentCount').textContent = Math.max(0, absentCount);
        document.getElementById('leaveCount').textContent = leaveCount;
    }

    showAttendanceModal(type) {
        const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));
        const title = document.getElementById('attendanceModalTitle');
        const form = document.getElementById('attendanceForm');
        
        title.textContent = type === 'check_in' ? 'تسجيل حضور' : 'تسجيل انصراف';
        form.reset();
        
        // Set current date and time
        form.querySelector('input[name="date"]').value = new Date().toISOString().split('T')[0];
        form.querySelector('input[name="time"]').value = new Date().toTimeString().split(':').slice(0, 2).join(':');
        
        this.attendanceType = type;
        modal.show();
    }

    saveAttendance() {
        const form = document.getElementById('attendanceForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // التحقق من البيانات المطلوبة
        if (!data.employee_id || !data.date || !data.time) {
            window.samApp.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // التحقق من صحة التاريخ
        const selectedDate = new Date(data.date);
        const today = new Date();
        today.setHours(23, 59, 59, 999); // نهاية اليوم

        if (selectedDate > today) {
            window.samApp.showAlert('لا يمكن تسجيل حضور لتاريخ مستقبلي', 'warning');
            return;
        }

        try {
            // البحث عن سجل حضور موجود لنفس الموظف والتاريخ
            const existingAttendance = Database.getAll('attendance').find(a =>
                a.employee_id === data.employee_id && a.date === data.date
            );

            if (existingAttendance) {
                // تحديث السجل الموجود
                const updateData = { ...existingAttendance };

                if (this.attendanceType === 'check_in') {
                    updateData.check_in = data.time;
                    updateData.status = 'present';
                } else if (this.attendanceType === 'check_out') {
                    updateData.check_out = data.time;
                    // حساب ساعات العمل وتحديد الحالة
                    const workingHours = this.calculateWorkingHours(updateData.check_in, data.time);
                    updateData.working_hours = workingHours;
                }

                if (data.notes) {
                    updateData.notes = data.notes;
                }

                updateData.updated_at = new Date().toISOString();

                Database.update('attendance', existingAttendance.id, updateData);
                window.samApp.showAlert(`تم تحديث ${this.attendanceType === 'check_in' ? 'وقت الحضور' : 'وقت الانصراف'} بنجاح`, 'success');
            } else {
                // إنشاء سجل جديد
                const attendanceData = {
                    employee_id: data.employee_id,
                    date: data.date,
                    status: 'present',
                    notes: data.notes || '',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                if (this.attendanceType === 'check_in') {
                    attendanceData.check_in = data.time;
                } else if (this.attendanceType === 'check_out') {
                    attendanceData.check_out = data.time;
                }

                Database.create('attendance', attendanceData);
                window.samApp.showAlert(`تم تسجيل ${this.attendanceType === 'check_in' ? 'الحضور' : 'الانصراف'} بنجاح`, 'success');
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('attendanceModal'));
            modal.hide();

            // تحديث البيانات
            this.loadAttendanceData();
            this.updateStats();

        } catch (error) {
            console.error('Error saving attendance:', error);
            window.samApp.showAlert('حدث خطأ أثناء حفظ البيانات: ' + error.message, 'danger');
        }
    }

    editAttendance(attendanceId) {
        const attendance = Database.getById('attendance', attendanceId);
        if (!attendance) {
            window.samApp.showAlert('سجل الحضور غير موجود', 'danger');
            return;
        }
        
        const modal = new bootstrap.Modal(document.getElementById('editAttendanceModal'));
        const form = document.getElementById('editAttendanceForm');
        
        // Populate form
        form.querySelector('input[name="check_in"]').value = attendance.check_in || '';
        form.querySelector('input[name="check_out"]').value = attendance.check_out || '';
        form.querySelector('select[name="status"]').value = attendance.status || '';
        form.querySelector('textarea[name="notes"]').value = attendance.notes || '';
        
        this.editingAttendanceId = attendanceId;
        modal.show();
    }

    updateAttendance() {
        const form = document.getElementById('editAttendanceForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        if (!this.editingAttendanceId) {
            window.samApp.showAlert('خطأ في تحديد السجل المراد تعديله', 'danger');
            return;
        }

        try {
            // الحصول على السجل الحالي
            const currentRecord = Database.getById('attendance', this.editingAttendanceId);
            if (!currentRecord) {
                window.samApp.showAlert('السجل غير موجود', 'danger');
                return;
            }

            // إعداد البيانات المحدثة
            const updatedData = {
                ...currentRecord,
                check_in: data.check_in || currentRecord.check_in,
                check_out: data.check_out || currentRecord.check_out,
                status: data.status || currentRecord.status,
                notes: data.notes || currentRecord.notes,
                updated_at: new Date().toISOString()
            };

            // حساب ساعات العمل إذا كان هناك وقت حضور وانصراف
            if (updatedData.check_in && updatedData.check_out) {
                updatedData.working_hours = this.calculateWorkingHours(updatedData.check_in, updatedData.check_out);

                // تحديد الحالة بناءً على أوقات العمل
                const employee = Database.getEmployee(currentRecord.employee_id);
                if (employee) {
                    const settings = Database.getSettings();
                    const startTime = settings.working_hours?.start_time || '08:00';
                    const lateThreshold = settings.attendance?.late_threshold || 15;

                    // التحقق من التأخير
                    const checkInTime = new Date(`2000-01-01T${updatedData.check_in}`);
                    const expectedStartTime = new Date(`2000-01-01T${startTime}`);
                    const lateMinutes = (checkInTime - expectedStartTime) / (1000 * 60);

                    if (lateMinutes > lateThreshold) {
                        updatedData.status = 'late';
                    } else if (data.status !== 'absent') {
                        updatedData.status = 'present';
                    }
                }
            }

            // تحديث السجل في قاعدة البيانات
            Database.update('attendance', this.editingAttendanceId, updatedData);

            const modal = bootstrap.Modal.getInstance(document.getElementById('editAttendanceModal'));
            modal.hide();

            window.samApp.showAlert('تم تحديث سجل الحضور بنجاح', 'success');

            // إرسال حدث تحديث الحضور
            const attendanceUpdateEvent = new CustomEvent('attendanceUpdated', {
                detail: {
                    employeeId: currentRecord.employee_id,
                    date: currentRecord.date,
                    month: currentRecord.date.substring(0, 7), // YYYY-MM format
                    recordId: this.editingAttendanceId,
                    updatedData: updatedData
                }
            });
            document.dispatchEvent(attendanceUpdateEvent);

            // تحديث البيانات
            this.loadAttendanceData();
            this.updateStats();

        } catch (error) {
            console.error('Error updating attendance:', error);
            window.samApp.showAlert('حدث خطأ أثناء تحديث السجل: ' + error.message, 'danger');
        }
    }

    deleteAttendance(attendanceId) {
        if (confirm('هل أنت متأكد من حذف سجل الحضور؟')) {
            try {
                Database.delete('attendance', attendanceId);
                window.samApp.showAlert('تم حذف سجل الحضور بنجاح', 'success');
                this.loadAttendanceData();
            } catch (error) {
                window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
            }
        }
    }

    exportAttendance() {
        // الحصول على البيانات المفلترة الحالية
        let attendance = [];

        switch (this.currentView) {
            case 'daily':
                attendance = Database.getAttendance({ date: this.selectedDate });
                break;
            case 'weekly':
                attendance = this.getWeekAttendance();
                break;
            case 'monthly':
                attendance = this.getMonthAttendance();
                break;
        }

        // تطبيق فلتر الموظف إذا كان محدداً
        if (this.selectedEmployee) {
            attendance = attendance.filter(a => a.employee_id === this.selectedEmployee);
        }

        if (attendance.length === 0) {
            window.samApp.showAlert('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        const employees = Database.getEmployees();

        const exportData = attendance.map(record => {
            const employee = employees.find(emp => emp.id === record.employee_id);
            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'التاريخ': record.date,
                'وقت الحضور': record.check_in || '',
                'وقت الانصراف': record.check_out || '',
                'ساعات العمل': this.calculateWorkingHours(record.check_in, record.check_out),
                'الحالة': record.status,
                'ملاحظات': record.notes || ''
            };
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الحضور والانصراف');

        // إنشاء اسم ملف يعكس الفلتر المطبق
        let fileName = 'attendance';
        if (this.selectedDate) {
            fileName += `_${this.selectedDate}`;
        } else if (this.selectedWeek) {
            fileName += `_week_${this.selectedWeek}`;
        } else if (this.selectedMonth) {
            fileName += `_month_${this.selectedMonth}`;
        } else {
            fileName += `_${new Date().toISOString().split('T')[0]}`;
        }

        if (this.selectedEmployee) {
            const employee = Database.getEmployee(this.selectedEmployee);
            if (employee) {
                fileName += `_${employee.employee_number}`;
            }
        }

        XLSX.writeFile(wb, `${fileName}.xlsx`);

        window.samApp.showAlert(`تم تصدير ${attendance.length} سجل حضور بنجاح`, 'success');
    }

    printAttendance() {
        // الحصول على البيانات المفلترة الحالية بدلاً من استدعاء قاعدة البيانات مرة أخرى
        let attendance = [];

        switch (this.currentView) {
            case 'daily':
                attendance = Database.getAttendance({ date: this.selectedDate });
                break;
            case 'weekly':
                attendance = this.getWeekAttendance();
                break;
            case 'monthly':
                attendance = this.getMonthAttendance();
                break;
        }

        // تطبيق فلتر الموظف إذا كان محدداً
        if (this.selectedEmployee) {
            attendance = attendance.filter(a => a.employee_id === this.selectedEmployee);
        }

        // إنشاء وصف مفصل للفلتر المطبق
        let filterDescription = this.generateFilterDescription();

        window.printManager.printAttendanceReport(attendance, filterDescription);
    }

    generateFilterDescription() {
        let description = '';

        // وصف نوع العرض والفترة
        switch (this.currentView) {
            case 'daily':
                if (this.selectedDate) {
                    description = `يوم ${window.samApp.formatDate(this.selectedDate)}`;
                } else {
                    description = 'اليوم الحالي';
                }
                break;
            case 'weekly':
                if (this.selectedWeek) {
                    const [year, weekStr] = this.selectedWeek.split('-W');
                    const weekNum = parseInt(weekStr);
                    const startDate = this.getDateOfWeek(parseInt(year), weekNum);
                    const endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);
                    description = `الأسبوع من ${window.samApp.formatDate(startDate.toISOString().split('T')[0])} إلى ${window.samApp.formatDate(endDate.toISOString().split('T')[0])}`;
                } else {
                    description = 'الأسبوع الحالي';
                }
                break;
            case 'monthly':
                if (this.selectedMonth) {
                    const [year, month] = this.selectedMonth.split('-');
                    const monthNames = [
                        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                    ];
                    description = `شهر ${monthNames[parseInt(month) - 1]} ${year}`;
                } else {
                    description = 'الشهر الحالي';
                }
                break;
        }

        // إضافة فلتر الموظف إذا كان محدداً
        if (this.selectedEmployee) {
            const employee = Database.getEmployee(this.selectedEmployee);
            if (employee) {
                description += ` - الموظف: ${employee.name} (${employee.employee_number})`;
            }
        } else {
            description += ' - جميع الموظفين';
        }

        return description;
    }

    // تنظيف الموارد عند مغادرة الصفحة
    cleanup() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }
}
