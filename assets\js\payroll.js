/**
 * SAM - نظام إدارة شؤون الموظفين
 * Payroll Management Module
 * وحدة إدارة الرواتب المبسطة
 */

class PayrollManager {
    constructor() {
        this.currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
        this.selectedEmployee = '';
        this.selectedStatus = '';
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();

        // إضافة تأثير الانتقال
        contentArea.classList.add('fade-in');

        this.bindEvents();
        this.loadPayrollData();
    }

    getMainHTML() {
        return `
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-money-bill-wave me-2"></i>إدارة الرواتب</h2>
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary" id="addPayrollBtn">
                                    <i class="fas fa-plus me-2"></i>إضافة راتب
                                </button>
                                <button type="button" class="btn btn-success" id="generateMonthlyBtn">
                                    <i class="fas fa-calendar-plus me-2"></i>إنشاء رواتب شهرية
                                </button>
                            </div>
                        </div>

                        <!-- فلاتر البحث -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">الشهر</label>
                                        <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الموظف</label>
                                        <select class="form-select" id="employeeFilter">
                                            <option value="">جميع الموظفين</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">جميع الحالات</option>
                                            <option value="pending">في الانتظار</option>
                                            <option value="paid">مدفوع</option>
                                            <option value="cancelled">ملغي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-primary w-100" id="filterBtn">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات الرواتب -->
                        <div class="row mb-4" id="payrollStats">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>

                        <!-- جدول الرواتب -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">كشوف الرواتب</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="payrollTable">
                                        <thead>
                                            <tr>
                                                <th>الموظف</th>
                                                <th>الشهر</th>
                                                <th>الراتب الأساسي</th>
                                                <th>البدلات</th>
                                                <th>الخصومات</th>
                                                <th>صافي الراتب</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها ديناميكياً -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل الراتب -->
            <div class="modal fade" id="payrollModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة راتب جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="payrollForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">الشهر *</label>
                                        <input type="month" class="form-control" name="month" required>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <label class="form-label">الراتب الأساسي</label>
                                        <input type="number" class="form-control" name="basic_salary" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">إجمالي البدلات</label>
                                        <input type="number" class="form-control" name="total_allowances" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">إجمالي الخصومات</label>
                                        <input type="number" class="form-control" name="total_deductions" step="0.01" readonly>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <label class="form-label">الراتب الإجمالي</label>
                                        <input type="number" class="form-control" name="gross_salary" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">صافي الراتب</label>
                                        <input type="number" class="form-control" name="net_salary" step="0.01" readonly>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-12">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="savePayrollBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // ربط الأحداث
        document.getElementById('addPayrollBtn').addEventListener('click', () => this.showPayrollModal());
        document.getElementById('generateMonthlyBtn').addEventListener('click', () => this.generateMonthlyPayroll());
        document.getElementById('filterBtn').addEventListener('click', () => this.loadPayrollData());
        document.getElementById('savePayrollBtn').addEventListener('click', () => this.savePayroll());
        
        // تحديث البيانات عند تغيير الموظف
        const employeeSelect = document.querySelector('#payrollForm select[name="employee_id"]');
        if (employeeSelect) {
            employeeSelect.addEventListener('change', () => this.loadEmployeeData());
        }

        // تحديث الفلاتر
        document.getElementById('monthFilter').addEventListener('change', () => this.loadPayrollData());
        document.getElementById('employeeFilter').addEventListener('change', () => this.loadPayrollData());
        document.getElementById('statusFilter').addEventListener('change', () => this.loadPayrollData());
    }

    loadPayrollData() {
        try {
            const month = document.getElementById('monthFilter').value || this.currentMonth;
            const employeeId = document.getElementById('employeeFilter').value;
            const status = document.getElementById('statusFilter').value;

            // جلب البيانات
            let payrolls = Database.getAll('payroll') || [];
            const employees = Database.getEmployees();

            // تطبيق الفلاتر
            if (month) {
                payrolls = payrolls.filter(p => p.month === month);
            }
            if (employeeId) {
                payrolls = payrolls.filter(p => p.employee_id === employeeId);
            }
            if (status) {
                payrolls = payrolls.filter(p => p.status === status);
            }

            // تحديث الإحصائيات
            this.updateStats(payrolls);

            // تحديث الجدول
            this.updateTable(payrolls, employees);

            // تحديث فلاتر الموظفين
            this.updateEmployeeFilters(employees);

        } catch (error) {
            console.error('خطأ في تحميل بيانات الرواتب:', error);
            window.samApp.showAlert('حدث خطأ في تحميل البيانات', 'danger');
        }
    }

    updateStats(payrolls) {
        const stats = {
            total: payrolls.length,
            totalBasic: payrolls.reduce((sum, p) => sum + (parseFloat(p.basic_salary) || 0), 0),
            totalAllowances: payrolls.reduce((sum, p) => sum + (parseFloat(p.total_allowances) || 0), 0),
            totalDeductions: payrolls.reduce((sum, p) => sum + (parseFloat(p.total_deductions) || 0), 0),
            totalNet: payrolls.reduce((sum, p) => sum + (parseFloat(p.net_salary) || 0), 0)
        };

        document.getElementById('payrollStats').innerHTML = `
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary">${stats.total}</h4>
                        <small>عدد الموظفين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info">${window.samApp.formatCurrency(stats.totalBasic)}</h4>
                        <small>إجمالي الرواتب الأساسية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success">${window.samApp.formatCurrency(stats.totalAllowances)}</h4>
                        <small>إجمالي البدلات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning">${window.samApp.formatCurrency(stats.totalDeductions)}</h4>
                        <small>إجمالي الخصومات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary">${window.samApp.formatCurrency(stats.totalNet)}</h4>
                        <small>صافي الرواتب</small>
                    </div>
                </div>
            </div>
        `;
    }

    updateTable(payrolls, employees) {
        const tbody = document.querySelector('#payrollTable tbody');

        if (payrolls.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">لا توجد كشوف رواتب</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            const statusBadge = this.getStatusBadge(payroll.status);

            return `
                <tr>
                    <td>${employee?.name || 'غير معروف'}</td>
                    <td>${payroll.month}</td>
                    <td>${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                    <td>${window.samApp.formatCurrency(payroll.total_allowances || 0)}</td>
                    <td>${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                    <td class="fw-bold">${window.samApp.formatCurrency(payroll.net_salary || 0)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="payrollManager.editPayroll('${payroll.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="payrollManager.markAsPaid('${payroll.id}')" title="تحديد كمدفوع">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="payrollManager.deletePayroll('${payroll.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getStatusBadge(status) {
        const statusMap = {
            'pending': '<span class="badge bg-warning">في الانتظار</span>',
            'paid': '<span class="badge bg-success">مدفوع</span>',
            'cancelled': '<span class="badge bg-danger">ملغي</span>'
        };
        return statusMap[status] || '<span class="badge bg-secondary">غير محدد</span>';
    }

    updateEmployeeFilters(employees) {
        const employeeFilter = document.getElementById('employeeFilter');
        const modalEmployeeSelect = document.querySelector('#payrollForm select[name="employee_id"]');

        const options = employees.filter(emp => emp.status === 'active').map(emp =>
            `<option value="${emp.id}">${emp.name} - ${emp.employee_number || ''}</option>`
        ).join('');

        if (employeeFilter) {
            employeeFilter.innerHTML = '<option value="">جميع الموظفين</option>' + options;
        }

        if (modalEmployeeSelect) {
            modalEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>' + options;
        }
    }

    showPayrollModal(payrollId = null) {
        const modal = new bootstrap.Modal(document.getElementById('payrollModal'));
        const form = document.getElementById('payrollForm');
        const title = document.querySelector('#payrollModal .modal-title');

        form.reset();

        if (payrollId) {
            title.textContent = 'تعديل الراتب';
            this.loadPayrollForEdit(payrollId);
        } else {
            title.textContent = 'إضافة راتب جديد';
            document.querySelector('#payrollForm input[name="month"]').value = this.currentMonth;
        }

        modal.show();
    }

    loadEmployeeData() {
        const employeeId = document.querySelector('#payrollForm select[name="employee_id"]').value;
        if (!employeeId) return;

        const employee = Database.getEmployee(employeeId);
        if (!employee) return;

        const form = document.getElementById('payrollForm');

        // الراتب الأساسي
        const basicSalary = parseFloat(employee.salary) || 0;
        form.querySelector('input[name="basic_salary"]').value = basicSalary;

        // حساب البدلات
        const totalAllowances = this.calculateSimpleAllowances(employee);
        form.querySelector('input[name="total_allowances"]').value = totalAllowances;

        // حساب الخصومات
        const totalDeductions = this.calculateSimpleDeductions(employee, basicSalary);
        form.querySelector('input[name="total_deductions"]').value = totalDeductions;

        // حساب الإجماليات
        const grossSalary = basicSalary + totalAllowances;
        const netSalary = Math.max(0, grossSalary - totalDeductions);

        form.querySelector('input[name="gross_salary"]').value = grossSalary;
        form.querySelector('input[name="net_salary"]').value = netSalary;
    }

    calculateSimpleAllowances(employee) {
        let total = 0;
        const basicSalary = parseFloat(employee.salary) || 0;

        try {
            // بدل السكن
            if (employee.housing_allowance_type === 'fixed') {
                const housingAllowance = parseFloat(employee.housing_allowance) || 0;
                total += housingAllowance;
            } else if (employee.housing_allowance_type === 'percentage') {
                const percentage = parseFloat(employee.housing_allowance_percentage) || 0;
                const housingAllowance = basicSalary * (percentage / 100);
                total += housingAllowance;
            }

            // بدل النقل
            if (employee.transport_allowance_type === 'fixed') {
                const transportAllowance = parseFloat(employee.transport_allowance) || 0;
                total += transportAllowance;
            } else if (employee.transport_allowance_type === 'daily') {
                const dailyTransport = parseFloat(employee.daily_transport_allowance) || 0;
                const workingDays = 22; // أيام العمل الافتراضية في الشهر
                total += dailyTransport * workingDays;
            }

            // بدل الطعام
            if (employee.food_allowance_type === 'fixed') {
                const foodAllowance = parseFloat(employee.food_allowance) || 0;
                total += foodAllowance;
            } else if (employee.food_allowance_type === 'daily') {
                const dailyFood = parseFloat(employee.daily_food_allowance) || 0;
                const workingDays = 22; // أيام العمل الافتراضية في الشهر
                total += dailyFood * workingDays;
            }

            // بدلات أخرى
            const otherAllowances = parseFloat(employee.other_allowances) || 0;
            total += otherAllowances;

            // التأكد من أن النتيجة موجبة
            total = Math.max(0, total);

        } catch (error) {
            console.error('خطأ في حساب البدلات:', error);
            total = 0;
        }

        return Math.round(total * 100) / 100;
    }

    calculateSimpleDeductions(employee, basicSalary) {
        let total = 0;

        try {
            const grossSalary = basicSalary + this.calculateSimpleAllowances(employee);

            // التأمينات الاجتماعية (9% من الراتب الأساسي)
            const socialInsuranceRate = 0.09; // 9%
            const socialInsurance = basicSalary * socialInsuranceRate;
            total += socialInsurance;

            // ضريبة الدخل المتدرجة
            let incomeTax = 0;
            if (grossSalary > 3000) {
                if (grossSalary <= 5000) {
                    // 5% للشريحة من 3001 إلى 5000
                    incomeTax = (grossSalary - 3000) * 0.05;
                } else if (grossSalary <= 10000) {
                    // 5% للشريحة الأولى + 10% للشريحة الثانية
                    incomeTax = (2000 * 0.05) + ((grossSalary - 5000) * 0.10);
                } else {
                    // 5% + 10% + 15% للشرائح العليا
                    incomeTax = (2000 * 0.05) + (5000 * 0.10) + ((grossSalary - 10000) * 0.15);
                }
            }
            total += incomeTax;

            // السلف والقروض (إذا كانت موجودة)
            const advances = this.getEmployeeAdvances(employee.id);
            total += advances;

            // الجزاءات المالية (إذا كانت موجودة)
            const penalties = this.getEmployeePenalties(employee.id);
            total += penalties;

            // خصومات أخرى
            const otherDeductions = parseFloat(employee.other_deductions) || 0;
            total += otherDeductions;

            // التأكد من أن النتيجة موجبة
            total = Math.max(0, total);

        } catch (error) {
            console.error('خطأ في حساب الخصومات:', error);
            total = 0;
        }

        return Math.round(total * 100) / 100;
    }

    // دالة مساعدة لحساب السلف النشطة
    getEmployeeAdvances(employeeId) {
        try {
            const advances = Database.getAll('advances') || [];
            const activeAdvances = advances.filter(advance =>
                advance.employee_id === employeeId &&
                advance.status === 'approved' &&
                advance.remaining_amount > 0
            );

            return activeAdvances.reduce((total, advance) => {
                const monthlyInstallment = parseFloat(advance.monthly_installment) || 0;
                return total + monthlyInstallment;
            }, 0);
        } catch (error) {
            console.warn('خطأ في جلب السلف:', error);
            return 0;
        }
    }

    // دالة مساعدة لحساب الجزاءات المالية
    getEmployeePenalties(employeeId) {
        try {
            const currentMonth = new Date().toISOString().slice(0, 7);
            const penalties = Database.getAll('penalties') || [];
            const monthPenalties = penalties.filter(penalty =>
                penalty.employee_id === employeeId &&
                penalty.month === currentMonth &&
                penalty.status === 'approved' &&
                penalty.type === 'financial'
            );

            return monthPenalties.reduce((total, penalty) => {
                return total + (parseFloat(penalty.amount) || 0);
            }, 0);
        } catch (error) {
            console.warn('خطأ في جلب الجزاءات:', error);
            return 0;
        }
    }

    savePayroll() {
        try {
            const form = document.getElementById('payrollForm');
            const formData = new FormData(form);
            const payrollId = form.dataset.payrollId;

            const payrollData = {
                employee_id: formData.get('employee_id'),
                month: formData.get('month'),
                basic_salary: parseFloat(formData.get('basic_salary')) || 0,
                total_allowances: parseFloat(formData.get('total_allowances')) || 0,
                total_deductions: parseFloat(formData.get('total_deductions')) || 0,
                gross_salary: parseFloat(formData.get('gross_salary')) || 0,
                net_salary: parseFloat(formData.get('net_salary')) || 0,
                notes: formData.get('notes') || '',
                updated_at: new Date().toISOString()
            };

            // التحقق من البيانات المطلوبة
            if (!payrollData.employee_id || !payrollData.month) {
                window.samApp.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            if (payrollId) {
                // تحديث راتب موجود
                const existingPayroll = Database.getById('payroll', payrollId);
                if (!existingPayroll) {
                    window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                    return;
                }

                // الاحتفاظ ببعض البيانات الأصلية
                payrollData.status = existingPayroll.status;
                payrollData.created_at = existingPayroll.created_at;
                payrollData.payment_date = existingPayroll.payment_date;

                Database.update('payroll', payrollId, payrollData);
                window.samApp.showAlert('تم تحديث الراتب بنجاح', 'success');

            } else {
                // إنشاء راتب جديد

                // التحقق من عدم وجود راتب مكرر
                const existingPayroll = Database.getAll('payroll').find(p =>
                    p.employee_id === payrollData.employee_id && p.month === payrollData.month
                );

                if (existingPayroll) {
                    window.samApp.showAlert('يوجد راتب مسجل لهذا الموظف في هذا الشهر', 'warning');
                    return;
                }

                payrollData.status = 'pending';
                payrollData.created_at = new Date().toISOString();

                Database.create('payroll', payrollData);
                window.samApp.showAlert('تم حفظ الراتب بنجاح', 'success');
            }

            // إغلاق النموذج وتحديث البيانات
            bootstrap.Modal.getInstance(document.getElementById('payrollModal')).hide();
            this.loadPayrollData();

            // مسح معرف الراتب من النموذج
            delete form.dataset.payrollId;

        } catch (error) {
            console.error('خطأ في حفظ الراتب:', error);
            window.samApp.showAlert('حدث خطأ في حفظ الراتب: ' + error.message, 'danger');
        }
    }

    generateMonthlyPayroll() {
        const month = prompt('أدخل الشهر (YYYY-MM):', this.currentMonth);
        if (!month) return;

        // التحقق من تنسيق الشهر
        if (!/^\d{4}-\d{2}$/.test(month)) {
            window.samApp.showAlert('تنسيق الشهر غير صحيح. يجب أن يكون بالتنسيق YYYY-MM', 'danger');
            return;
        }

        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        if (employees.length === 0) {
            window.samApp.showAlert('لا يوجد موظفون نشطون لإنشاء كشوف رواتب', 'warning');
            return;
        }

        const existingPayrolls = Database.getAll('payroll').filter(p => p.month === month);

        if (existingPayrolls.length > 0) {
            if (!confirm(`يوجد ${existingPayrolls.length} كشف راتب لهذا الشهر. هل تريد المتابعة وإنشاء كشوف للموظفين المتبقين؟`)) {
                return;
            }
        }

        let created = 0;
        let skipped = 0;

        employees.forEach(employee => {
            // التحقق من عدم وجود راتب مسبق
            const existing = existingPayrolls.find(p => p.employee_id === employee.id);
            if (existing) {
                skipped++;
                return;
            }

            try {
                const basicSalary = parseFloat(employee.salary) || 0;
                const totalAllowances = this.calculateSimpleAllowances(employee);
                const totalDeductions = this.calculateSimpleDeductions(employee, basicSalary);
                const grossSalary = basicSalary + totalAllowances;
                const netSalary = Math.max(0, grossSalary - totalDeductions);

                const payrollData = {
                    employee_id: employee.id,
                    month: month,
                    basic_salary: basicSalary,
                    total_allowances: totalAllowances,
                    total_deductions: totalDeductions,
                    gross_salary: grossSalary,
                    net_salary: netSalary,
                    status: 'pending',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                Database.create('payroll', payrollData);
                created++;

            } catch (error) {
                console.error(`خطأ في إنشاء راتب للموظف ${employee.name}:`, error);
            }
        });

        window.samApp.showAlert(`تم إنشاء ${created} كشف راتب جديد. تم تخطي ${skipped} موظف لوجود كشوف رواتب مسبقة.`, 'success');
        this.loadPayrollData();
    }

    editPayroll(payrollId) {
        this.showPayrollModal(payrollId);
    }

    loadPayrollForEdit(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        if (!payroll) {
            window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
            return;
        }

        const form = document.getElementById('payrollForm');

        // ملء البيانات
        form.querySelector('select[name="employee_id"]').value = payroll.employee_id;
        form.querySelector('input[name="month"]').value = payroll.month;
        form.querySelector('input[name="basic_salary"]').value = payroll.basic_salary || 0;
        form.querySelector('input[name="total_allowances"]').value = payroll.total_allowances || 0;
        form.querySelector('input[name="total_deductions"]').value = payroll.total_deductions || 0;
        form.querySelector('input[name="gross_salary"]').value = payroll.gross_salary || 0;
        form.querySelector('input[name="net_salary"]').value = payroll.net_salary || 0;
        form.querySelector('textarea[name="notes"]').value = payroll.notes || '';

        // حفظ معرف الراتب للتحديث
        form.dataset.payrollId = payrollId;
    }

    markAsPaid(payrollId) {
        if (!confirm('هل أنت متأكد من تحديد هذا الراتب كمدفوع؟')) {
            return;
        }

        try {
            const payroll = Database.getById('payroll', payrollId);
            if (!payroll) {
                window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                return;
            }

            const updatedData = {
                ...payroll,
                status: 'paid',
                payment_date: new Date().toISOString().split('T')[0],
                updated_at: new Date().toISOString()
            };

            Database.update('payroll', payrollId, updatedData);
            window.samApp.showAlert('تم تحديد الراتب كمدفوع بنجاح', 'success');
            this.loadPayrollData();

        } catch (error) {
            console.error('خطأ في تحديث حالة الراتب:', error);
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    deletePayroll(payrollId) {
        if (!confirm('هل أنت متأكد من حذف كشف الراتب؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            Database.delete('payroll', payrollId);
            window.samApp.showAlert('تم حذف كشف الراتب بنجاح', 'success');
            this.loadPayrollData();

        } catch (error) {
            console.error('خطأ في حذف كشف الراتب:', error);
            window.samApp.showAlert('حدث خطأ في حذف كشف الراتب: ' + error.message, 'danger');
        }
    }
}

// إنشاء مثيل من مدير الرواتب
window.payrollManager = new PayrollManager();
